// 多用户聊天测试脚本
// 这个脚本可以在浏览器控制台中运行，用于测试多用户聊天功能

const io = require('socket.io-client');

class ChatTester {
    constructor(userId, userName) {
        this.userId = userId;
        this.userName = userName;
        this.socket = null;
        this.messages = [];
    }

    async connect() {
        return new Promise((resolve, reject) => {
            this.socket = io('http://localhost:3001');
            
            this.socket.on('connect', () => {
                console.log(`${this.userName} 已连接到服务器`);
                
                // 加入聊天室
                this.socket.emit('join_room', {
                    roomId: 'security-chat',
                    userInfo: {
                        id: this.userId,
                        name: this.userName,
                        avatar: '/default-avatar.svg'
                    }
                });
                
                resolve();
            });

            this.socket.on('connect_error', (error) => {
                console.error(`${this.userName} 连接失败:`, error);
                reject(error);
            });

            this.socket.on('message', (data) => {
                this.messages.push(data);
                console.log(`${this.userName} 收到消息:`, data);
            });

            this.socket.on('user_joined', (data) => {
                console.log(`${this.userName} 看到用户加入:`, data);
            });

            this.socket.on('user_left', (data) => {
                console.log(`${this.userName} 看到用户离开:`, data);
            });
        });
    }

    sendMessage(content) {
        if (!this.socket) {
            console.error(`${this.userName} 未连接到服务器`);
            return false;
        }

        const message = {
            type: 'text',
            content: content,
            roomId: 'security-chat',
            sender: {
                id: this.userId,
                name: this.userName,
                avatar: '/default-avatar.svg'
            },
            timestamp: new Date().toISOString()
        };

        this.socket.emit('send_message', message);
        console.log(`${this.userName} 发送消息: ${content}`);
        return true;
    }

    disconnect() {
        if (this.socket) {
            this.socket.disconnect();
            console.log(`${this.userName} 已断开连接`);
        }
    }

    getMessages() {
        return this.messages;
    }
}

// 测试函数
async function testMultiUserChat() {
    console.log('开始多用户聊天测试...');
    
    // 创建多个测试用户
    const users = [
        new ChatTester('user-1', '测试用户1'),
        new ChatTester('user-2', '测试用户2'),
        new ChatTester('user-3', '测试用户3')
    ];

    try {
        // 连接所有用户
        console.log('连接所有用户...');
        await Promise.all(users.map(user => user.connect()));
        
        // 等待一秒让连接稳定
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        // 等待消息传播
        await new Promise(resolve => setTimeout(resolve, 2000));
        
        // 检查消息接收情况
        console.log('\n=== 消息接收统计 ===');
        users.forEach((user, index) => {
            console.log(`${user.userName} 收到 ${user.getMessages().length} 条消息:`);
            user.getMessages().forEach(msg => {
                console.log(`  - ${msg.sender?.name}: ${msg.content}`);
            });
        });
        
        // 断开所有连接
        users.forEach(user => user.disconnect());
        
        console.log('\n测试完成！');
        
    } catch (error) {
        console.error('测试失败:', error);
        users.forEach(user => user.disconnect());
    }
}

// 如果在Node.js环境中运行
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { ChatTester, testMultiUserChat };
}

// 如果在浏览器中运行
if (typeof window !== 'undefined') {
    window.ChatTester = ChatTester;
    window.testMultiUserChat = testMultiUserChat;
    
    console.log('聊天测试工具已加载！');
    console.log('使用方法：');
    console.log('1. testMultiUserChat() - 运行自动化测试');
    console.log('2. 手动创建用户: const user = new ChatTester("user-id", "用户名")');
    console.log('3. 连接: await user.connect()');
    console.log('4. 发送消息: user.sendMessage("消息内容")');
}

// 简单的浏览器测试函数
function quickBrowserTest() {
    console.log('开始浏览器快速测试...');
    
    // 模拟发送消息到当前页面的聊天组件
    if (typeof window !== 'undefined' && window.socketService) {
        const testMessage = {
            type: 'text',
            content: '这是一条测试消息 - ' + new Date().toLocaleTimeString(),
            roomId: 'security-chat',
            sender: {
                id: 'test-user-' + Date.now(),
                name: '测试用户',
                avatar: '/default-avatar.svg'
            }
        };
        
        window.socketService.sendMessage(testMessage);
        console.log('测试消息已发送');
    } else {
        console.log('未找到socketService，请确保在聊天页面运行此测试');
    }
}

if (typeof window !== 'undefined') {
    window.quickBrowserTest = quickBrowserTest;
}
