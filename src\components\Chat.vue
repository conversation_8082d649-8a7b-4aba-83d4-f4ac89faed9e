<template>
  <div class="chat-container">
    <!-- 顶部导航栏 -->
    <div class="chat-header">
      <div class="header-left">
        <button class="back-btn" @click="goBack">
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
            <path d="M15 18L9 12L15 6" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
        </button>
      </div>
      <div class="header-center">
        <div class="contact-info">
          <img :src="contactAvatar" :alt="contactName" class="contact-avatar">
          <div class="contact-details">
            <span class="contact-name">{{ contactName }}</span>
            <span class="connection-status" :class="{ connected: isConnected }">
              {{ isConnected ? '在线' : '连接中...' }}
            </span>
          </div>
        </div>
      </div>
      <div class="header-right">
        <button class="more-btn">
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
            <circle cx="12" cy="12" r="1" fill="currentColor"/>
            <circle cx="19" cy="12" r="1" fill="currentColor"/>
            <circle cx="5" cy="12" r="1" fill="currentColor"/>
          </svg>
        </button>
      </div>
    </div>

    <!-- 聊天内容区域 -->
    <div class="chat-content" ref="chatContent">
      <div class="message-list">
        <div 
          v-for="message in messages" 
          :key="message.id" 
          :class="['message-item', message.isSelf ? 'message-self' : 'message-other']"
        >
          <div class="message-avatar" v-if="!message.isSelf">
            <img :src="message.sender?.avatar || contactAvatar" :alt="message.sender?.name || contactName">
          </div>
          <div class="message-content">
            <div class="sender-name">
              {{ !message.isSelf ? userName : (message.sender?.name || contactName) }}
            </div>
            <div class="message-bubble" :class="message.type">
              <!-- 文本消息 -->
              <div v-if="message.type === 'text'" class="text-message">
                {{ message.content }}
              </div>
              <!-- 图片消息 -->
              <div v-else-if="message.type === 'image'" class="image-message">
                <img :src="message.content" :alt="'图片消息'" @click="previewImage(message.content)">
              </div>
              <!-- 语音消息 -->
              <div v-else-if="message.type === 'voice'" class="voice-message">
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                  <path d="M12 1C10.9 1 10 1.9 10 3V12C10 13.1 10.9 14 12 14S14 13.1 14 12V3C14 1.9 13.1 1 12 1Z" fill="currentColor"/>
                  <path d="M19 10V12C19 16.4 15.4 20 11 20H13C17.4 20 21 16.4 21 12V10H19Z" fill="currentColor"/>
                  <path d="M7 12V10H5V12C5 16.4 8.6 20 13 20H11C6.6 20 3 16.4 3 12Z" fill="currentColor"/>
                </svg>
                <span>{{ message.duration }}''</span>
              </div>
            </div>
            <div class="message-time">{{ formatTime(message.timestamp) }}</div>
          </div>
          <div class="message-avatar" v-if="message.isSelf">
            <img :src="userAvatar" :alt="userName">
          </div>
        </div>
      </div>
    </div>

    <!-- 底部输入区域 -->
    <div class="chat-input">
      <div class="input-toolbar">
        <!-- 语音输入按钮 -->
        <button 
          class="toolbar-btn voice-btn" 
          :class="{ active: isVoiceMode }"
          @click="toggleVoiceMode"
        >
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
            <path d="M12 1C10.9 1 10 1.9 10 3V12C10 13.1 10.9 14 12 14S14 13.1 14 12V3C14 1.9 13.1 1 12 1Z" stroke="currentColor" stroke-width="2"/>
            <path d="M19 10V12C19 16.4 15.4 20 11 20H13C17.4 20 21 16.4 21 12V10" stroke="currentColor" stroke-width="2"/>
            <path d="M7 12V10M5 12C5 16.4 8.6 20 13 20H11C6.6 20 3 16.4 3 12" stroke="currentColor" stroke-width="2"/>
          </svg>
        </button>

        <!-- 文本输入框 -->
        <div class="input-wrapper" v-if="!isVoiceMode">
          <textarea
            ref="messageInput"
            v-model="inputMessage"
            class="message-input"
            placeholder="输入消息..."
            rows="1"
            @keydown="handleKeydown"
            @input="adjustTextareaHeight"
          ></textarea>
        </div>

        <!-- 语音输入区域 -->
        <div class="voice-input-wrapper" v-else>
          <button 
            class="voice-record-btn"
            :class="{ recording: isRecording }"
            @mousedown="startRecording"
            @mouseup="stopRecording"
            @mouseleave="stopRecording"
            @touchstart="startRecording"
            @touchend="stopRecording"
          >
            {{ isRecording ? '松开结束' : '按住说话' }}
          </button>
        </div>

        <!-- 表情按钮 -->
        <button 
          class="toolbar-btn emoji-btn" 
          :class="{ active: showEmojiPicker }"
          @click="toggleEmojiPicker"
          v-if="!isVoiceMode"
        >
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
            <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="2"/>
            <path d="M8 14S9.5 16 12 16S16 14 16 14" stroke="currentColor" stroke-width="2"/>
            <line x1="9" y1="9" x2="9.01" y2="9" stroke="currentColor" stroke-width="2"/>
            <line x1="15" y1="9" x2="15.01" y2="9" stroke="currentColor" stroke-width="2"/>
          </svg>
        </button>

        <!-- 图片上传按钮 -->
        <button class="toolbar-btn image-btn" @click="selectImage" v-if="!isVoiceMode">
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
            <rect x="3" y="3" width="18" height="18" rx="2" ry="2" stroke="currentColor" stroke-width="2"/>
            <circle cx="8.5" cy="8.5" r="1.5" stroke="currentColor" stroke-width="2"/>
            <path d="M21 15L16 10L5 21" stroke="currentColor" stroke-width="2"/>
          </svg>
        </button>

        <!-- 发送按钮 -->
        <button 
          class="send-btn" 
          :disabled="!canSend"
          @click="sendMessage"
          v-if="!isVoiceMode"
        >
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
            <line x1="22" y1="2" x2="11" y2="13" stroke="currentColor" stroke-width="2"/>
            <polygon points="22,2 15,22 11,13 2,9 22,2" fill="currentColor"/>
          </svg>
        </button>
      </div>

      <!-- 表情选择器 -->
      <div class="emoji-picker" v-if="showEmojiPicker">
        <div class="emoji-grid">
          <button 
            v-for="emoji in emojiList" 
            :key="emoji" 
            class="emoji-item"
            @click="insertEmoji(emoji)"
          >
            {{ emoji }}
          </button>
        </div>
      </div>
    </div>

    <!-- 隐藏的文件输入 -->
    <input 
      type="file" 
      ref="fileInput" 
      accept="image/*" 
      style="display: none" 
      @change="handleImageSelect"
    >
  </div>
</template>

<script>
import { ref, onMounted, onUnmounted, nextTick, computed } from 'vue'
import { useRouter } from 'vue-router'
import socketService from '../utils/socket.js'

export default {
  name: 'Chat',
  setup() {
    const router = useRouter()
    
    // 响应式数据
    const messages = ref([])
    const inputMessage = ref('')
    const isVoiceMode = ref(false)
    const isRecording = ref(false)
    const showEmojiPicker = ref(false)
    const isConnected = ref(false)
    
    // 联系人信息
    const contactName = ref('admin')
    const contactAvatar = ref('/src/assets/images/default-avatar.svg')
    const userName = ref('用户' + Math.floor(Math.random() * 1000))
    const userAvatar = ref('/src/assets/images/user-avatar.svg')
    const currentUserId = ref('user-' + Date.now() + '-' + Math.floor(Math.random() * 1000))
    
    // DOM引用
    const chatContent = ref(null)
    const messageInput = ref(null)
    const fileInput = ref(null)
    
    // 表情列表
    const emojiList = [
      '😀', '😃', '😄', '😁', '😆', '😅', '😂', '🤣',
      '😊', '😇', '🙂', '🙃', '😉', '😌', '😍', '🥰',
      '😘', '😗', '😙', '😚', '😋', '😛', '😝', '😜',
      '🤪', '🤨', '🧐', '🤓', '😎', '🤩', '🥳', '😏',
      '😒', '😞', '😔', '😟', '😕', '🙁', '☹️', '😣',
      '😖', '😫', '😩', '🥺', '😢', '😭', '😤', '😠'
    ]
    
    // 计算属性
    const canSend = computed(() => {
      return inputMessage.value.trim().length > 0
    })
    
    // 方法
    const goBack = () => {
      router.go(-1)
    }
    
    const toggleVoiceMode = () => {
      isVoiceMode.value = !isVoiceMode.value
      showEmojiPicker.value = false
    }
    
    const toggleEmojiPicker = () => {
      showEmojiPicker.value = !showEmojiPicker.value
    }
    
    const insertEmoji = (emoji) => {
      inputMessage.value += emoji
      showEmojiPicker.value = false
      nextTick(() => {
        messageInput.value?.focus()
      })
    }
    
    const selectImage = () => {
      fileInput.value?.click()
    }
    
    const handleImageSelect = (event) => {
      const file = event.target.files[0]
      if (file) {
        const reader = new FileReader()
        reader.onload = (e) => {
          sendImageMessage(e.target.result)
        }
        reader.readAsDataURL(file)
      }
    }
    
    const sendMessage = () => {
      if (!canSend.value) return

      const message = {
        id: Date.now(),
        type: 'text',
        content: inputMessage.value.trim(),
        isSelf: true,
        timestamp: new Date()
      }

      // 发送到Socket.io服务器
      const success = socketService.sendMessage({
        type: 'text',
        content: message.content,
        roomId: 'security-chat',
        sender: {
          id: currentUserId.value,
          name: userName.value,
          avatar: userAvatar.value
        }
      })

      if (success) {
        messages.value.push(message)
        inputMessage.value = ''
        scrollToBottom()
      } else {
        console.error('Failed to send message')
        // 可以显示错误提示
      }
    }
    
    const sendImageMessage = (imageUrl) => {
      const message = {
        id: Date.now(),
        type: 'image',
        content: imageUrl,
        isSelf: true,
        timestamp: new Date()
      }

      // 发送到Socket.io服务器
      const success = socketService.sendMessage({
        type: 'image',
        content: imageUrl,
        roomId: 'security-chat',
        sender: {
          id: currentUserId.value,
          name: userName.value,
          avatar: userAvatar.value
        }
      })

      if (success) {
        messages.value.push(message)
        scrollToBottom()
      } else {
        console.error('Failed to send image message')
      }
    }
    
    const startRecording = () => {
      isRecording.value = true
      // TODO: 实现语音录制逻辑
    }
    
    const stopRecording = () => {
      if (isRecording.value) {
        isRecording.value = false
        // TODO: 处理录制完成的语音
      }
    }
    
    const handleKeydown = (event) => {
      if (event.key === 'Enter' && !event.shiftKey) {
        event.preventDefault()
        sendMessage()
      }
    }
    
    const adjustTextareaHeight = () => {
      const textarea = messageInput.value
      if (textarea) {
        textarea.style.height = 'auto'
        textarea.style.height = Math.min(textarea.scrollHeight, 120) + 'px'
      }
    }
    
    const scrollToBottom = () => {
      nextTick(() => {
        if (chatContent.value) {
          chatContent.value.scrollTop = chatContent.value.scrollHeight
        }
      })
    }
    
    const formatTime = (timestamp) => {
      const date = new Date(timestamp)
      const now = new Date()
      const today = new Date(now.getFullYear(), now.getMonth(), now.getDate())
      const yesterday = new Date(today.getTime() - 24 * 60 * 60 * 1000)
      const messageDate = new Date(date.getFullYear(), date.getMonth(), date.getDate())

      // 如果是今天，只显示时间
      if (messageDate.getTime() === today.getTime()) {
        return date.toLocaleTimeString('zh-CN', {
          hour: '2-digit',
          minute: '2-digit'
        })
      }
      // 如果是昨天，显示"昨天"
      else if (messageDate.getTime() === yesterday.getTime()) {
        return '昨天'
      }
      // 如果是更早的日期，显示完整日期时间
      else {
        const year = date.getFullYear().toString().slice(-2) // 取年份后两位
        const month = (date.getMonth() + 1).toString().padStart(2, '0')
        const day = date.getDate().toString().padStart(2, '0')
        const hour = date.getHours().toString().padStart(2, '0')
        const minute = date.getMinutes().toString().padStart(2, '0')
        return `${year}/${month}/${day} ${hour}:${minute}`
      }
    }
    
    const previewImage = (imageUrl) => {
      // TODO: 实现图片预览功能
      console.log('预览图片:', imageUrl)
    }
    
    // 初始化示例消息
    const initMessages = () => {
      messages.value = [
        {
          id: 1,
          type: 'text',
          content: '您好，有什么可以帮助您的吗？',
          isSelf: false,
          timestamp: new Date(Date.now() - 300000)
        },
        {
          id: 2,
          type: 'text',
          content: '我想了解一下系统的使用情况',
          isSelf: true,
          timestamp: new Date(Date.now() - 240000)
        },
      ]
    }
    
    // Socket.io消息处理
    const handleIncomingMessage = (data) => {
      // 不显示自己发送的消息（避免重复）
      if (data.sender && data.sender.id === currentUserId.value) {
        return
      }

      const message = {
        id: data.id || Date.now(),
        type: data.type || 'text',
        content: data.content,
        isSelf: false,
        timestamp: new Date(data.timestamp || Date.now()),
        sender: data.sender
      }

      messages.value.push(message)
      scrollToBottom()
    }

    const handleUserJoined = (data) => {
      console.log('User joined:', data)
      // 可以显示用户加入提示
    }

    const handleUserLeft = (data) => {
      console.log('User left:', data)
      // 可以显示用户离开提示
    }

    // 初始化Socket.io连接
    const initSocket = async () => {
      try {
        await socketService.connect('http://localhost:3001')
        isConnected.value = true

        // 注册消息处理器
        socketService.on('message', handleIncomingMessage)
        socketService.on('user_joined', handleUserJoined)
        socketService.on('user_left', handleUserLeft)

        // 加入聊天室
        socketService.joinRoom('security-chat', {
          id: currentUserId.value,
          name: userName.value,
          avatar: userAvatar.value
        })

        console.log('Socket.io initialized successfully')
      } catch (error) {
        console.error('Failed to initialize Socket.io:', error)
        isConnected.value = false
        // 可以显示连接失败提示
      }
    }

    onMounted(() => {
      initMessages()
      scrollToBottom()
      initSocket()
    })

    onUnmounted(() => {
      // 清理Socket.io连接
      socketService.off('message', handleIncomingMessage)
      socketService.off('user_joined', handleUserJoined)
      socketService.off('user_left', handleUserLeft)
      socketService.leaveRoom('security-chat')
      socketService.disconnect()
    })
    
    return {
      // 数据
      messages,
      inputMessage,
      isVoiceMode,
      isRecording,
      showEmojiPicker,
      isConnected,
      contactName,
      contactAvatar,
      userName,
      userAvatar,
      currentUserId,
      emojiList,

      // DOM引用
      chatContent,
      messageInput,
      fileInput,

      // 计算属性
      canSend,

      // 方法
      goBack,
      toggleVoiceMode,
      toggleEmojiPicker,
      insertEmoji,
      selectImage,
      handleImageSelect,
      sendMessage,
      sendImageMessage,
      startRecording,
      stopRecording,
      handleKeydown,
      adjustTextareaHeight,
      scrollToBottom,
      formatTime,
      previewImage
    }
  }
}
</script>

<style scoped>
.chat-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  width: 100vw;
  background-color: #f5f5f5;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 1000;
  overflow: hidden;
}

/* 顶部导航栏 */
.chat-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  background-color: #ffffff;
  border-bottom: 1px solid #e5e5e5;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  position: sticky;
  top: 0;
  z-index: 100;
}

.header-left, .header-right {
  flex: 0 0 auto;
}

.header-center {
  flex: 1;
  display: flex;
  justify-content: center;
}

.back-btn, .more-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border: none;
  background: none;
  color: #333;
  cursor: pointer;
  border-radius: 8px;
  transition: background-color 0.2s;
}

.back-btn:hover, .more-btn:hover {
  background-color: #f0f0f0;
}

.contact-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.contact-avatar {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  object-fit: cover;
}

.contact-details {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.contact-name {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  line-height: 1.2;
}

.connection-status {
  font-size: 12px;
  color: #999;
  line-height: 1.2;
  margin-top: 2px;
}

.connection-status.connected {
  color: #07c160;
}

/* 聊天内容区域 */
.chat-content {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
  background-color: #f5f5f5;
  min-height: 0; /* 确保flex子项能够正确收缩 */
}

.message-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.message-item {
  display: flex;
  align-items: flex-end;
  gap: 8px;
  max-width: 80%;
}

.message-item.message-self {
  align-self: flex-end;
  flex-direction: row-reverse;
}

.message-item.message-other {
  align-self: flex-start;
}

.message-avatar {
  flex-shrink: 0;
}

.message-avatar img {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  object-fit: cover;
}

.message-content {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.sender-name {
  font-size: 12px;
  color: #999;
  margin-bottom: 2px;
  margin-left: 12px;
}

.message-self .sender-name {
  margin-left: 0;
  margin-right: 12px;
  text-align: right;
}

.message-self .message-content {
  align-items: flex-end;
}

.message-other .message-content {
  align-items: flex-start;
}

.message-bubble {
  max-width: 100%;
  padding: 12px 16px;
  border-radius: 18px;
  word-wrap: break-word;
  position: relative;
}

.message-self .message-bubble {
  background-color: #07c160;
  color: white;
}

.message-other .message-bubble {
  background-color: white;
  color: #333;
  border: 1px solid #e5e5e5;
}

.text-message {
  line-height: 1.4;
  font-size: 16px;
}

.image-message {
  padding: 4px;
}

.image-message img {
  max-width: 200px;
  max-height: 200px;
  border-radius: 8px;
  cursor: pointer;
  display: block;
}

.voice-message {
  display: flex;
  align-items: center;
  gap: 8px;
  min-width: 80px;
}

.message-time {
  font-size: 12px;
  color: #999;
  margin: 0 8px;
}

/* 底部输入区域 */
.chat-input {
  background-color: #ffffff;
  border-top: 1px solid #e5e5e5;
  padding: 12px 16px;
}

.input-toolbar {
  display: flex;
  align-items: flex-end;
  gap: 12px;
}

.toolbar-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  border: none;
  background: none;
  color: #666;
  cursor: pointer;
  border-radius: 8px;
  transition: all 0.2s;
  flex-shrink: 0;
}

.toolbar-btn:hover {
  background-color: #f0f0f0;
  color: #333;
}

.toolbar-btn.active {
  color: #07c160;
  background-color: #f0f9f5;
}

.input-wrapper {
  flex: 1;
  display: flex;
  align-items: flex-end;
}

.message-input {
  flex: 1;
  min-height: 36px;
  max-height: 120px;
  padding: 8px 12px;
  border: 1px solid #e5e5e5;
  border-radius: 18px;
  font-size: 16px;
  line-height: 1.4;
  resize: none;
  outline: none;
  font-family: inherit;
  background-color: #ffffff;
  transition: border-color 0.2s;
}

.message-input:focus {
  border-color: #07c160;
}

.voice-input-wrapper {
  flex: 1;
  display: flex;
  justify-content: center;
}

.voice-record-btn {
  flex: 1;
  height: 36px;
  border: 1px solid #e5e5e5;
  border-radius: 18px;
  background-color: #ffffff;
  color: #666;
  font-size: 16px;
  cursor: pointer;
  transition: all 0.2s;
  user-select: none;
}

.voice-record-btn:active,
.voice-record-btn.recording {
  background-color: #07c160;
  color: white;
  border-color: #07c160;
}

.send-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  border: none;
  background-color: #07c160;
  color: white;
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.2s;
  flex-shrink: 0;
}

.send-btn:hover:not(:disabled) {
  background-color: #06ad56;
}

.send-btn:disabled {
  background-color: #ccc;
  cursor: not-allowed;
}

/* 表情选择器 */
.emoji-picker {
  margin-top: 12px;
  padding: 16px;
  background-color: #ffffff;
  border: 1px solid #e5e5e5;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.emoji-grid {
  display: grid;
  grid-template-columns: repeat(8, 1fr);
  gap: 8px;
  max-height: 200px;
  overflow-y: auto;
}

.emoji-item {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border: none;
  background: none;
  font-size: 20px;
  cursor: pointer;
  border-radius: 6px;
  transition: background-color 0.2s;
}

.emoji-item:hover {
  background-color: #f0f0f0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .chat-content {
    padding: 12px;
  }

  .message-item {
    max-width: 85%;
  }

  .contact-name {
    font-size: 15px;
  }

  .text-message {
    font-size: 15px;
  }

  .emoji-grid {
    grid-template-columns: repeat(6, 1fr);
  }
}

/* 滚动条样式 */
.chat-content::-webkit-scrollbar {
  width: 4px;
}

.chat-content::-webkit-scrollbar-track {
  background: transparent;
}

.chat-content::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 2px;
}

.chat-content::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.3);
}

.emoji-grid::-webkit-scrollbar {
  width: 4px;
}

.emoji-grid::-webkit-scrollbar-track {
  background: #f5f5f5;
}

.emoji-grid::-webkit-scrollbar-thumb {
  background: #ccc;
  border-radius: 2px;
}
</style>
